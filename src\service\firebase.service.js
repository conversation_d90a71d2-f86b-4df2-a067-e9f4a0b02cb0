import AppInstance from "config/axios.config";

async function createUser(payload) {
  const response = await AppInstance({
    method: "POST",
    url: "/createUser",
    data: payload,
  });
  return response;
}

async function assignAdminPermission(payload) {
  const response = await AppInstance({
    method: "POST",
    url: "/assignAdminPermission",
    data: payload,
  });
  return response;
}

async function createAdminUser(payload) {
  const response = await AppInstance({
    method: "POST",
    url: "/createAdminUser",
    data: payload,
  });
  return response;
}

const firebaseService = {
  createUser,
  assignAdminPermission,
  createAdminUser,
};

export { firebaseService };
