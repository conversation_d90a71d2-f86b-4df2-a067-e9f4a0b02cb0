import axios from "axios";
import { auth } from "./firebase.config";

const AppInstance = axios.create({
  baseURL: "https://us-central1-insyt-care.cloudfunctions.net",
});

AppInstance.interceptors.request.use(async (request) => {
  try {
    // Get current user from Firebase Auth
    const currentUser = auth.currentUser;
    if (currentUser) {
      // Get fresh ID token (this will automatically refresh if needed)
      const idToken = await currentUser.getIdToken(true);
      request.headers.Authorization = `Bearer ${idToken}`;
      // Update localStorage with fresh token
      localStorage.setItem("access_token", idToken);
    } else {
      // Fallback to stored token if no current user
      const accessToken = localStorage.getItem("access_token");
      if (accessToken) {
        request.headers.Authorization = `Bearer ${accessToken}`;
      }
    }
  } catch (error) {
    console.error("Error getting ID token:", error);
    // Fallback to stored token
    const accessToken = localStorage.getItem("access_token");
    if (accessToken) {
      request.headers.Authorization = `Bearer ${accessToken}`;
    }
  }
  return request;
});

AppInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle 401 errors by clearing stored tokens
    if (error.response?.status === 401) {
      localStorage.removeItem("access_token");
      localStorage.removeItem("userId");
      // Optionally redirect to login page
      // window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default AppInstance;
