export const preventDefault = () => {
  document.querySelectorAll("form").forEach((form) => {
    form.addEventListener("submit", (e) => e.preventDefault());
  });
  document.querySelectorAll('a[href="#"]').forEach((a) => {
    a.addEventListener("click", (e) => e.preventDefault());
  });
};

export function generateAlphabet() {
  return [...Array(26)].map((_, i) => String.fromCharCode(i + 97));
}

export const convertToDataURL = async (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
    reader.readAsDataURL(file);
  });
};

export function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];
}

export function generateRandomString(length = 3) {
  const characters = "abcdefghijklmnopqrstuvwxyz123456789";
  let result = "";
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
}

export function getNameInitials(...names) {
  // Filter out empty strings and get first letter of each name
  const initials = names
    .filter((name) => name && typeof name === "string")
    .map((name) => name.trim().charAt(0).toUpperCase());

  // If we have at least 2 initials, return first 2
  if (initials.length >= 2) {
    return initials.slice(0, 2).join("");
  }

  // If we have only 1 initial, return it
  if (initials.length === 1) {
    return initials[0];
  }

  // If no valid names provided, return empty string
  return "";
}

export function generateNames(name) {
  if (!name) return { firstName: "", lastName: "" };
  const nameParts = name?.trim().split(" ");
  const firstName = nameParts[0] || "";
  const lastName = nameParts?.slice(1)?.join(" ") || "";
  return { firstName, lastName };
}

/**
 * Formats an address object into a properly structured display format
 * @param {Object} address - Address object with components
 * @returns {string} Formatted address string with proper line breaks
 */
export function formatAddress(address) {
  if (!address) return "";

  // If formattedAddress exists and looks properly formatted, use it
  if (address.formattedAddress && !address.formattedAddress.includes(',')) {
    return address.formattedAddress;
  }

  // Build address components
  const streetParts = [
    address.streetNumber,
    address.streetName,
    address.addressLine2
  ].filter(Boolean);

  const locationParts = [
    address.city,
    address.state,
    address.country
  ].filter(Boolean);

  // Create formatted address with proper structure
  const addressLines = [];

  // Add street address (if available)
  if (streetParts.length > 0) {
    addressLines.push(streetParts.join(' '));
  }

  // Add city, state, country (if available)
  if (locationParts.length > 0) {
    addressLines.push(locationParts.join(', '));
  }

  // If no components available, fall back to formattedAddress
  if (addressLines.length === 0 && address.formattedAddress) {
    return address.formattedAddress;
  }

  return addressLines.join(',\n');
}
