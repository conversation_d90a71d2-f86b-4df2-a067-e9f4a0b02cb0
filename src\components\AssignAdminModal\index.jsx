import React, { useState } from "react";
import ModalWindow, { ModalContent } from "@components/ModalWindow";
import {
  Button,
  CircularProgress,
  Divider,
  Typography,
  Box,
  Alert,
  TextField,
  IconButton,
  InputAdornment,
  Tooltip,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import { Visibility, VisibilityOff, ContentCopy, CheckCircle } from "@mui/icons-material";
import { Controller, useForm } from "react-hook-form";
import styled from "styled-components";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { textSizes } from "@styles/vars";
import { useSelector, useDispatch } from "react-redux";
import { createAdminUser } from "@store/slices/users";
import { useSnackbar } from "notistack";

const StyledModalContent = styled(ModalContent)`
  max-width: 700px;
  width: 100%;
  margin:  auto;
  padding: 16px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;

  @media (min-width: 768px) {
    width: 90%;
    padding: 24px;
    margin:auto;
    
  }

  @media (min-width: 1024px) {
    width: 700px;
    padding: 32px;
    margin:auto;
    
  }

  @media (min-width: 1280px) {
    height: auto;
    min-height: auto;
    margin:auto;
    
  }

  /* Ensure modal doesn't go off screen on small devices */
  @media (max-height: 600px) {
    margin: 10px auto;
    top: 10px;
    max-height: 95vh;
  }
`;

const StyledTextField = styled(TextField)`
  // margin-bottom: 16px;

  .MuiOutlinedInput-root {
    border-radius: 8px;
    margin-bottom: 16px;
  }

  @media (max-width: 768px) {
    .MuiInputLabel-root { 
      font-size: 14px;
    }

    .MuiOutlinedInput-input {
      font-size: 14px;
      padding: 12px 14px;
    }
  }
`;

const CredentialsBox = styled(Box)`
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  background-color: #f9f9f9;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    padding: 12px;
  }
`;

const CredentialRow = styled(Box)`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
`;

const CredentialLabel = styled(Typography)`
  font-weight: 600;
  color: #666;
  min-width: 80px;

  @media (max-width: 768px) {
    font-size: 12px;
    min-width: auto;
  }
`;

const CredentialValue = styled(Typography)`
  flex: 1;
  margin: 0 12px;
  word-break: break-all;

  @media (max-width: 768px) {
    margin: 0;
    font-size: 14px;
  }
`;

const createAdminSchema = z.object({
  name: z.string().min(1, "Name is required").min(2, "Name must be at least 2 characters"),
  email: z.string().min(1, "Email is required").email("Please enter a valid email address"),
  password: z.string().min(1, "Password is required").min(6, "Password must be at least 6 characters"),
});

const AssignAdminModal = ({ isVisible, onClose }) => {
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [confirmationStep, setConfirmationStep] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [copiedField, setCopiedField] = useState(null);

  const { user: currentUser } = useSelector((state) => state.auth);

  const {
    handleSubmit,
    control,
    formState: { errors },
    reset,
    watch,
  } = useForm({
    defaultValues: {
      name: "",
      email: "",
      password: "",
    },
    resolver: zodResolver(createAdminSchema),
    mode: "onChange",
  });

  const formData = watch();

  const handleClose = () => {
    reset();
    setConfirmationStep(false);
    setIsSubmitting(false);
    setShowPassword(false);
    setCopiedField(null);
    onClose();
  };

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleCopyToClipboard = async (text, fieldName) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(fieldName);
      enqueueSnackbar(`${fieldName} copied to clipboard`, { variant: "success" });

      // Reset the copied state after 2 seconds
      setTimeout(() => {
        setCopiedField(null);
      }, 2000);
    } catch (error) {
      enqueueSnackbar("Failed to copy to clipboard", { variant: "error" });
    }
  };

  const handleCopyAllCredentials = async () => {
    const credentials = `Admin User Credentials:  
Name: ${formData.name}
Email: ${formData.email}
Password: ${formData.password}
Role: Admin`;

    try {
      await navigator.clipboard.writeText(credentials);
      enqueueSnackbar("All credentials copied to clipboard", { variant: "success" });
    } catch (error) {
      enqueueSnackbar("Failed to copy credentials", { variant: "error" });
    }
  };

  const onSubmit = async (data) => {
    if (!confirmationStep) {
      setConfirmationStep(true);
      return;
    }

    setIsSubmitting(true);

    try {
      await dispatch(
        createAdminUser({
          name: data.name,
          email: data.email,
          password: data.password,
        }),
      ).unwrap();

      enqueueSnackbar(`Successfully created admin user: ${data.name}`, {
        variant: "success",
      });

      handleClose();
    } catch (error) {
      console.error("Error creating admin user:", error);

      let errorMessage = "Failed to create admin user";

      // Handle specific error scenarios
      if (error?.response?.status === 403) {
        errorMessage = "You don't have permission to create admin users";
      } else if (error?.response?.status === 409) {
        errorMessage = "A user with this email address already exists";
      } else if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error?.message) {
        errorMessage = error.message;
      } else if (error?.code === "auth/insufficient-permission") {
        errorMessage = "Insufficient permissions to perform this action";
      } else if (error?.code === "auth/email-already-exists") {
        errorMessage = "A user with this email address already exists";
      }

      enqueueSnackbar(errorMessage, { variant: "error" });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBack = () => {
    setConfirmationStep(false);
  };

  // Authorization check - only allow admin users
  if (currentUser?.role !== "ADMIN") {
    return (
      <ModalWindow isVisible={isVisible} visibilityHandler={handleClose}>
        <StyledModalContent>
          <Typography
            variant={isMobile ? "h6" : "h5"}
            component="h2"
            gutterBottom
            sx={{
              fontSize: { xs: "18px", md: "24px" },
              fontWeight: 600,
              textAlign: { xs: "center", md: "left" },
            }}
          >
            Access Denied
          </Typography>
          <Divider sx={{ mb: { xs: 2, md: 3 } }} />
          <Alert severity="error">
            <Typography sx={{ fontSize: { xs: "14px", md: "16px" } }}>
              You don't have permission to create admin users. Only existing admin users can perform this action.
            </Typography>
          </Alert>
          <Box
            display="flex"
            justifyContent="flex-end"
            mt={3}
            sx={{ justifyContent: { xs: "center", md: "flex-end" } }}
          >
            <Button variant="outlined" onClick={handleClose} fullWidth={isMobile} size={isMobile ? "large" : "medium"}>
              Close
            </Button>
          </Box>
        </StyledModalContent>
      </ModalWindow>
    );
  }

  return (
    <ModalWindow isVisible={isVisible} visibilityHandler={handleClose}>
      <StyledModalContent sx={{ marginTop: "15px" }}>
        <Typography
          variant={isMobile ? "h6" : "h5"}
          component="h2"
          gutterBottom
          sx={{
            fontSize: { xs: "18px", md: "24px" },
            fontWeight: 600,
            textAlign: { xs: "center", md: "left" },
          }}
        >
          {confirmationStep ? "Confirm Admin Creation" : "Create New Admin"}
        </Typography>

        <Divider sx={{ mb: { xs: 2, md: 3 } }} />

        {!confirmationStep ? (
          <form onSubmit={handleSubmit(onSubmit)}>
            <Box mb={3}>
              <Typography
                variant="body2"
                color="text.secondary"
                gutterBottom
                sx={{ fontSize: { xs: "14px", md: "16px",marginBottom: "10px" } }}
              >
                Create a new admin by providing their name, email, and password.
              </Typography>

              <Controller
                name="name"
                control={control}
                render={({ field }) => (
                  <StyledTextField
                    {...field}
                    fullWidth
                    label="Full Name"
                    placeholder="Enter admin user's full name"
                    error={!!errors.name}
                    helperText={errors.name?.message}
                    variant="outlined"
                    size={isMobile ? "small" : "medium"}
                  />
                )}
              />

              <Controller
                name="email"
                control={control}
                render={({ field }) => (
                  <StyledTextField
                    {...field}
                    fullWidth
                    label="Email Address"
                    placeholder="Enter admin user's email"
                    type="email"
                    error={!!errors.email}
                    helperText={errors.email?.message}
                    variant="outlined"
                    size={isMobile ? "small" : "medium"}
                  />
                )}
              />

              <Controller
                name="password"
                control={control}
                render={({ field }) => (
                  <StyledTextField
                    {...field}
                    fullWidth
                    label="Password"
                    placeholder="Enter a secure password (min 6 characters)"
                    type={showPassword ? "text" : "password"}
                    error={!!errors.password}
                    helperText={errors.password?.message}
                    variant="outlined"
                    size={isMobile ? "small" : "medium"}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <Tooltip title={showPassword ? "Hide password" : "Show password"}>
                            <IconButton
                              onClick={handleTogglePasswordVisibility}
                              edge="end"
                              size={isMobile ? "small" : "medium"}
                            >
                              {showPassword ? <VisibilityOff /> : <Visibility />}
                            </IconButton>
                          </Tooltip>
                        </InputAdornment>
                      ),
                    }}
                  />
                )}
              />
            </Box>

            <Box display="flex" gap={2} justifyContent="flex-end" flexDirection={isMobile ? "column" : "row"}>
              <Button
                variant="outlined"
                onClick={handleClose}
                disabled={isSubmitting}
                fullWidth={isMobile}
                size={isMobile ? "large" : "medium"}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                disabled={!formData.name || !formData.email || !formData.password || isSubmitting}
                fullWidth={isMobile}
                size={isMobile ? "large" : "medium"}
              >
                Continue
              </Button>
            </Box>
          </form>
        ) : (
          <Box>
            <Alert severity="warning" sx={{ mb: 3 }}>
              <Typography variant="body2" sx={{ fontSize: { xs: "14px", md: "16px" } }}>
                <strong>Warning:</strong> You are about to create a new admin user. Admin users will have full access to
                the system including the ability to manage all users, appointments, and system settings.
              </Typography>
            </Alert>

            <Box mb={3}>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="subtitle2" sx={{ fontSize: { xs: "14px", md: "16px" } }}>
                  Admin user to be created:
                </Typography>
                <Tooltip title="Copy all credentials">
                  <IconButton
                    onClick={handleCopyAllCredentials}
                    size="small"
                    sx={{
                      bgcolor: "primary.main",
                      color: "white",
                      "&:hover": { bgcolor: "primary.dark" },
                    }}
                  >
                    <ContentCopy fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>

              <CredentialsBox>
                <CredentialRow>
                  <CredentialLabel variant="body2">Name:</CredentialLabel>
                  <CredentialValue variant="body1" sx={{ fontWeight: 600 }}>
                    {formData.name}
                  </CredentialValue>
                  <Tooltip title="Copy name">
                    <IconButton onClick={() => handleCopyToClipboard(formData.name, "Name")} size="small">
                     
                    </IconButton>
                  </Tooltip>
                </CredentialRow>

                <CredentialRow>
                  <CredentialLabel variant="body2">Email:</CredentialLabel>
                  <CredentialValue variant="body2">{formData.email}</CredentialValue>
                  <Tooltip title="Copy email">
                    <IconButton onClick={() => handleCopyToClipboard(formData.email, "Email")} size="small">
                   
                    </IconButton>
                  </Tooltip>
                </CredentialRow>

                <CredentialRow>
                  <CredentialLabel variant="body2">Password:</CredentialLabel>
                  <CredentialValue
                    variant="body2"
                    sx={{
                      fontFamily: "monospace",
                      letterSpacing: showPassword ? "normal" : "2px",
                    }}
                  >
                    {showPassword ? formData.password : "•".repeat(formData.password.length)}
                  </CredentialValue>
                  <Box display="flex" gap={0.5}>
                    <Tooltip title={showPassword ? "Hide password" : "Show password"}>
                      <IconButton onClick={handleTogglePasswordVisibility} size="small">
                        {showPassword ? <VisibilityOff fontSize="small" /> : <Visibility fontSize="small" />}
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Copy password">
                      <IconButton onClick={() => handleCopyToClipboard(formData.password, "Password")} size="small">
                        
                      </IconButton>
                    </Tooltip>
                  </Box>
                </CredentialRow>

                <CredentialRow>
                  <CredentialLabel variant="body2">Role:</CredentialLabel>
                  <CredentialValue variant="body2" sx={{ fontWeight: 600, color: "primary.main" }}>
                    Admin
                  </CredentialValue>
                  <Box width={40} /> {/* Spacer to align with other rows */}
                </CredentialRow>
              </CredentialsBox>

              <Alert severity="info" sx={{ mt: 2 }}>
                <Typography variant="body2" sx={{ fontSize: { xs: "12px", md: "14px" } }}>
                  <strong>Important:</strong> Please save these credentials securely. You won't be able to view the
                  password again after creation.
                </Typography>
              </Alert>
            </Box>

            <Box display="flex" gap={2} justifyContent="flex-end" flexDirection={isMobile ? "column" : "row"}>
              <Button
                variant="outlined"
                onClick={handleBack}
                disabled={isSubmitting}
                fullWidth={isMobile}
                size={isMobile ? "large" : "medium"}
              >
                Back
              </Button>
              <Button
                variant="contained"
                color="primary"
                onClick={handleSubmit(onSubmit)}
                disabled={isSubmitting}
                startIcon={isSubmitting ? <CircularProgress size={20} /> : null}
                fullWidth={isMobile}
                size={isMobile ? "large" : "medium"}
              >
                {isSubmitting ? "Creating..." : "Create Admin"}
              </Button>
            </Box>
          </Box>
        )}
      </StyledModalContent>
    </ModalWindow>
  );
};

export default AssignAdminModal;
